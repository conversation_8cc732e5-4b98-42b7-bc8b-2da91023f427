
const notProps  = [
  { label: '标题', prop: 'label' , width: '150px' },
  { label: '内容', prop: 'content' , width: '1000px' }
];

const notTableData = [
  {
    label: '通知标题1',
    content: '通知内容1',
  },
  {
    label: '通知标题2',
    content: '通知内容2',
  },
  {
    label: '通知标题1',
    content: '通知内容1',
  },
  {
    label: '通知标题2',
    content: '通知内容2',
  },{
    label: '通知标题1',
    content: '通知内容1',
  },
  {
    label: '通知标题2',
    content: '通知内容2',
  },{
    label: '通知标题1',
    content: '通知内容1',
  },
  {
    label: '通知标题2',
    content: '通知内容2',
  },{
    label: '通知标题1',
    content: '通知内容1',
  },
  {
    label: '通知标题2',
    content: '通知内容2',
  }
];

const briefingTableData = [{
  briefingContent: '简报内容1',
  type: 'info',
  tag: '领导关注'
}, {
  briefingContent: '简报内容2',
  type: 'danger',
  tag: '重点'
}, {
  briefingContent: '简报内容3',
  type: 'success',
  tag: '注意事项'
}, {
  briefingContent: '简报内容4',
  type: 'waring',
  tag: '纪律'
},{
  briefingContent: '简报内容1',
  type: 'info'
}, {
  briefingContent: '简报内容2',
  type: 'danger'
}, {
  briefingContent: '简报内容3',
  type: 'success'
}, {
  briefingContent: '简报内容4',
  type: 'waring'
},{
  briefingContent: '简报内容1',
  type: 'info'
}, {
  briefingContent: '简报内容2',
  type: 'danger'
}, {
  briefingContent: '简报内容3',
  type: 'success'
}, {
  briefingContent: '简报内容4',
  type: 'waring'
},{
  briefingContent: '简报内容1',
  type: 'info'
}, {
  briefingContent: '简报内容2',
  type: 'danger'
}, {
  briefingContent: '简报内容3',
  type: 'success'
}, {
  briefingContent: '简报内容4',
  type: 'waring'
}];

const busList = [
  {
    title: '业务名称1',
    content: '业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1业务描述1'
  },
  {
    title: '业务名称1',
    content: '业务描述1'
  },
  {
    title: '业务名称1',
    content: '业务描述1'
  }
]

const entranceList = [{
  url: 'https://www.baidu.com',
  name: '快速入口',
  imgUrl: ''
}, {
  url: 'https://www.baidu.com',
  name: '快速入口',
  imgUrl: ''
}, {
  url: 'https://www.baidu.com',
  name: '快速入口',
  imgUrl: ''
}, {
  url: 'https://www.baidu.com',
  name: '快速入口',
  imgUrl: ''
}];

const subscribeProps = [
  { label: '消息来源', prop: 'label' , width: '150px' },
  { label: '消息描述', prop: 'content' , width: '400px' }
];

const  subscribeTableData = [{
  label: '消息来源1',
  content: '消息描述1',
  status: true
}, {
  label: '消息来源2',
  content: '消息描述2',
  status: false
}, {
  label: '消息来源3',
  content: '消息描述3',
  status: true
}, {
  label: '消息来源4',
  content: '消息描述4',
  status: false
},{
  label: '消息来源1',
  content: '消息描述1',
  status: true
}, {
  label: '消息来源2',
  content: '消息描述2',
  status: false
}, {
  label: '消息来源3',
  content: '消息描述3',
  status: true
}, {
  label: '消息来源4',
  content: '消息描述4',
  status: false
},{
  label: '消息来源1',
  content: '消息描述1',
  status: true
}, {
  label: '消息来源2',
  content: '消息描述2',
  status: false
}, {
  label: '消息来源3',
  content: '消息描述3',
  status: true
}, {
  label: '消息来源4',
  content: '消息描述4',
  status: false
},{
  label: '消息来源1',
  content: '消息描述1',
  status: true
}, {
  label: '消息来源2',
  content: '消息描述2',
  status: false
}, {
  label: '消息来源3',
  content: '消息描述3',
  status: true
}, {
  label: '消息来源4',
  content: '消息描述4',
  status: false
},{
  label: '消息来源1',
  content: '消息描述1',
  status: true
}, {
  label: '消息来源2',
  content: '消息描述2',
  status: false
}, {
  label: '消息来源3',
  content: '消息描述3',
  status: true
}, {
  label: '消息来源4',
  content: '消息描述4',
  status: false
}];

const tagList = [{
  label: '公共',
  value: '1',
  num: 1
},{
  label: '公共',
  value: '1',
  num: 1
},{
  label: '公共',
  value: '1',
  num: 1
},{
  label: '公共',
  value: '1',
  num: 1
},{
  label: '公共',
  value: '1',
  num: 1
},{
  label: '公共',
  value: '1',
  num: 1
},{
  label: '公共',
  value: '1',
  num: 1
},{
  label: '公共',
  value: '1',
  num: 1
},{
  label: '公共',
  value: '1',
  num: 1
}];

export default {
  notProps,
  notTableData,
  briefingTableData,
  busList,
  entranceList,
  subscribeProps,
  subscribeTableData,
  tagList
}
