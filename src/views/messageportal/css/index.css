.border {
  border: 1px solid #000000;
}

.message-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
}

.message-top-left {
  display: flex;
  align-items: center;
}

.message-top-left-title {
  font-size: 20px;
  font-weight: bold;
  margin-left: 10px;
}

.message-top-right {
  display: flex;
  align-items: center;
}

.briefing-flex {
  display: flex;
  flex-direction: column;
}

.briefing-size {
  font-size: 16px;
  margin: 5px 0px;
  border-bottom: 1px solid #FFF;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 3px;
  padding-bottom: 3px;
}

.card-header-box-height {
  height: 30px;
  display: flex;
  align-items: center;
}

.notice {
  display: flex;
  flex-wrap: wrap;
}

.notice .notice-card {
  width: 19.3%;
  height: 140px;
  border: 1px solid #FFF;
  border-radius: 5px;
  margin: 5px 3px;
  padding: 3px;
}

.notice .notice-card .title {
  font-size: 16px;
  font-weight: bold;
  text-align: center;
}

.notice .notice-card .content {
  font-size: 13px;
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 6; /* 根据高度设置行数 */
  -webkit-box-orient: vertical;
  text-indent: 25px;
  line-height: 19px;
  font-style: italic;
}

.entrance {
  display: flex;
  flex-wrap: wrap;
}

.entrance .entrance-card {
  width: 18%;
  height: 75px;
  border-radius: 5px;
  margin: 5px;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center
}

.entrance .entrance-card p {
  font-size: 13px;
  margin: 1px;
}

.el-badge-box {
  margin: 5px 10px;
}
