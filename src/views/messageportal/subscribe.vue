<template>
  <MessageTable :props="subscribeProps" :height="540" :table-data="subscribeTableData">
    <template v-slot:table-but="{ row }">
      <div style="display: flex;">
        <div style="color: #548DDB;margin-right: 10px;">订阅</div>
        <div style="color: crimson;margin-right: 10px;">取消订阅</div>
      </div>
    </template>
  </MessageTable>
</template>

<script>
import MessageTable from './components/messageTable.vue'
import dataJs from './js/index'

export default {
  name: "subscribe",
  components: {
    MessageTable
  },
  data() {
    return {
      subscribeProps: [],
      subscribeTableData: []
    }
  },
  created() {
    this.subscribeProps = dataJs.subscribeProps;
    this.subscribeTableData = dataJs.subscribeTableData;
  }
}
</script>

<style scoped>

</style>
