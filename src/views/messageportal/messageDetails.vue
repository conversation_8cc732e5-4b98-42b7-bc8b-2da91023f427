<template>
  <div>
<!--    <h1 style="text-align: center;"> {{ model.label  }} </h1>-->
    <div class="prop-content" v-for="item in props">
      <label style="width: 10%">{{ item.label }}：</label>
      <div style="width: 90%">{{ model[item.prop] }}</div>
    </div>
  </div>
</template>

<script>
import dataJs from './js/index'

export default {
  name: "messageDetails",
  props: {
    model: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      props: [],
    }
  },
  created() {
    this.props = dataJs.notProps;
  },
  methods: {

  }
}
</script>

<style scoped>
  .prop-content {
    margin: 10px;
    display: flex;
  }
</style>
