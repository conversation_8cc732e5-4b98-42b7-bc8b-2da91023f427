<template>
    <div class="el-table el-table--fit el-table--enable-row-transition el-table--medium"
         style="width: 100%;">
      <div class="el-table__body-wrapper is-scrolling" :style="`height: ${height}px;overflow: auto;`">
        <table cellspacing="0" cellpadding="0" border="0" class="el-table__body" style="width: 100%;">
          <colgroup>
            <col name="index" width="60">
            <col v-for="(item, index) in props" :name="`el-table_1_column_${index + 1}`" :width="item.width">
            <col name="index" width="120">
          </colgroup>
          <thead class="has-gutter">
            <tr style="height:38px;">
              <th class="el-table_index  is_hidden is-leaf el-table__cell index">
                <div class="cell" style="line-height: 32px;">序号</div>
              </th>
              <th
                colspan="1"
                rowspan="1"
                :class="`el-table_1_column_${index + 1} is_hidden is-leaf el-table__cell`"
                v-for="(item, index) in props"
              >
                <div class="cell" style="line-height: 32px;">{{ item.label }}</div>
              </th>
              <th colspan="1" rowspan="1" class="flex is-leaf el-table__cell">
                <div class="cell" style="line-height: 32px;">操作</div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr style="height:38px;"
                v-for="(row, index) in tableData"
                class="el-table__row"
                :key="index">
              <td>
                <div class="cell" style="line-height: 32px;">{{ index + 1 }}</div>
              </td>
              <td v-for="item in props">
                <div class="cell" style="line-height: 32px;">
                  {{ row[item.prop] }}
                </div>
              </td>
              <td>
                <div class="cell" style="line-height: 32px;">
                  <slot name="table-but" :row="row"></slot>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="el-table__empty-block"
             style="height: 100%; width: 100%;"
             v-if="!tableData || tableData.length == 0">
          <span class="el-table__empty-text">暂无数据</span>
        </div>
        <div class="el-table__column-resize-proxy" style="display: none;"></div>
      </div>
    </div>
</template>

<script>
export default {
  name: "messageTable",
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    props: {
      type: Array,
      default: () => []
    },
    height: {
      type: Number,
      default: () => 230
    }
  },
  data() {
    return {};
  },
  created() {
  },
  methods: {}
}
</script>

<style scoped>
tr td:last-child, th:last-child {
  position: sticky;
  right: 0;
  z-index: 1;
}

thead tr {
  position: sticky;
  top: 0;
  z-index: 2;
  background: #104895;
}
</style>
