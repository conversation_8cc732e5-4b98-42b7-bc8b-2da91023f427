<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>设备检测数据上传</h2>
      <p class="page-description">上传Excel文件进行设备检测数据解析</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-number">{{ statistics.total }}</div>
            <div class="statistics-label">总数据量</div>
          </div>
          <i class="el-icon-document statistics-icon"></i>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-number">{{ statistics.success }}</div>
            <div class="statistics-label">解析成功</div>
          </div>
          <i class="el-icon-success statistics-icon success"></i>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-number">{{ statistics.pending }}</div>
            <div class="statistics-label">待解析</div>
          </div>
          <i class="el-icon-time statistics-icon warning"></i>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-number">{{ statistics.failed }}</div>
            <div class="statistics-label">解析失败</div>
          </div>
          <i class="el-icon-error statistics-icon danger"></i>
        </el-card>
      </el-col>
    </el-row>

    <!-- 上传区域 -->
    <el-card class="upload-card">
      <div slot="header" class="card-header">
        <span>文件上传</span>
        <el-button
          type="text"
          icon="el-icon-question"
          @click="showHelp = true"
        >
          使用说明
        </el-button>
      </div>

      <el-form :model="uploadForm" :rules="uploadRules" ref="uploadForm" label-width="100px">
        <el-form-item label="设备编码" prop="deviceCode">
          <el-select
            v-model="uploadForm.deviceCode"
            placeholder="请选择设备"
            filterable
            style="width: 300px"
          >
            <el-option
              v-for="option in deviceOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="Excel文件" prop="file">
          <el-upload
            ref="upload"
            class="upload-demo"
            action="#"
            drag
            :auto-upload="false"
            :limit="1"
            :file-list="fileList"
            :on-change="handleFileChange"
            accept=".xlsx,.xls"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">
              只能上传 .xlsx/.xls 文件，且不超过 10MB
            </div>
          </el-upload>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="uploadForm.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :rows="3"
            style="width: 500px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitUpload" :loading="uploading">
            <i class="el-icon-upload2"></i>
            开始上传
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 最近上传记录 -->
    <el-card class="recent-uploads">
      <div slot="header" class="card-header">
        <span>最近上传记录</span>
        <el-button type="text" @click="$router.push('/detection/data/list')">查看全部</el-button>
      </div>

      <el-table :data="recentUploads" style="width: 100%">
        <el-table-column prop="fileName" label="文件名" show-overflow-tooltip />
        <el-table-column prop="deviceCode" label="设备编码" width="120" />
        <el-table-column prop="templateName" label="模板名称" width="150" show-overflow-tooltip />
        <el-table-column prop="parseStatus" label="解析状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.parseStatus)">
              {{ getStatusText(scope.row.parseStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="上传时间" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="viewDetail(scope.row.id)">查看</el-button>
            <el-button
              size="mini"
              type="text"
              v-if="scope.row.parseStatus === 2"
              @click="reparse(scope.row.id)"
            >重新解析</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 使用说明对话框 -->
    <el-dialog
      title="使用说明"
      :visible.sync="showHelp"
      width="600px"
    >
      <div class="help-content">
        <h4>数据上传流程</h4>
        <ol>
          <li>选择设备类型和对应的检测模板</li>
          <li>下载Excel模板文件</li>
          <li>在Excel模板中填写检测数据</li>
          <li>上传填写好的Excel文件</li>
          <li>系统自动解析数据并显示预览</li>
          <li>确认数据无误后完成导入</li>
        </ol>

        <h4>注意事项</h4>
        <ul>
          <li>请严格按照模板格式填写数据</li>
          <li>必填字段不能为空</li>
          <li>数字字段请填写有效数字</li>
          <li>日期字段请使用正确的日期格式</li>
          <li>选择字段的值必须在预定义选项中</li>
          <li>文件大小不能超过10MB</li>
        </ul>

        <h4>常见问题</h4>
        <el-collapse>
          <el-collapse-item title="上传失败怎么办？" name="1">
            <p>请检查文件格式是否正确，确保是Excel文件(.xlsx或.xls)，文件大小不超过10MB。</p>
          </el-collapse-item>
          <el-collapse-item title="数据解析错误怎么办？" name="2">
            <p>请检查数据格式是否符合模板要求，特别注意必填字段、数据类型和选项值。</p>
          </el-collapse-item>
          <el-collapse-item title="如何修改已上传的数据？" name="3">
            <p>可以在检测数据管理页面中查看和编辑已上传的数据。</p>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-dialog>

    <!-- 上传成功提示 -->
    <el-dialog
      title="上传成功"
      :visible.sync="successVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="success-content">
        <i class="el-icon-success" style="color: #67c23a; font-size: 48px;"></i>
        <p>数据上传成功！</p>
        <p>您可以在检测数据管理页面查看上传的数据。</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="successVisible = false">关闭</el-button>
        <el-button type="primary" @click="goToDataList">查看数据</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  uploadExcelFile,
  getParseStatusStatistics,
  listDeviceDetectionData,
  reparseExcelFile
} from '@/api/analysis/deviceDetectionData'
import { getBindOptions } from '@/api/analysis/deviceTemplateBinding'

export default {
  name: 'DataUpload',
  data() {
    return {
      // 统计数据
      statistics: {
        total: 0,
        success: 0,
        pending: 0,
        failed: 0
      },
      // 上传表单
      uploadForm: {
        deviceCode: '',
        file: null,
        remark: ''
      },
      // 表单验证规则
      uploadRules: {
        deviceCode: [
          { required: true, message: '请选择设备', trigger: 'change' }
        ]
      },
      // 设备选项
      deviceOptions: [],
      // 文件列表
      fileList: [],
      // 上传状态
      uploading: false,
      // 最近上传记录
      recentUploads: [],
      // 显示帮助
      showHelp: false,
      // 成功提示
      successVisible: false
    }
  },
  created() {
    this.getStatistics()
    this.getDeviceOptions()
    this.getRecentUploads()
  },
  methods: {
    /** 获取统计数据 */
    getStatistics() {
      getParseStatusStatistics().then(response => {
        if (response.code === 200) {
          this.statistics = response.data
        }
      })
    },
    /** 获取设备选项 */
    getDeviceOptions() {
      getBindOptions().then(response => {
        if (response.code === 200) {
          this.deviceOptions = response.data || []
        }
      })
    },
    /** 获取最近上传记录 */
    getRecentUploads() {
      listDeviceDetectionData({ pageNum: 1, pageSize: 5 }).then(response => {
        if (response.code === 200) {
          this.recentUploads = response.data.records || []
        }
      })
    },
    /** 文件变化处理 */
    handleFileChange(file, fileList) {
      this.uploadForm.file = file.raw
    },
    /** 提交上传 */
    submitUpload() {
      this.$refs.uploadForm.validate(valid => {
        if (valid) {
          if (!this.uploadForm.file) {
            this.$message.error('请选择要上传的文件')
            return
          }

          // 验证文件类型和大小
          const isExcel = this.uploadForm.file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                         this.uploadForm.file.type === 'application/vnd.ms-excel'
          const isLt10M = this.uploadForm.file.size / 1024 / 1024 < 10

          if (!isExcel) {
            this.$message.error('只能上传 Excel 文件!')
            return
          }
          if (!isLt10M) {
            this.$message.error('上传文件大小不能超过 10MB!')
            return
          }

          this.uploading = true

          // 创建FormData
          const formData = new FormData()
          formData.append('file', this.uploadForm.file)
          formData.append('deviceCode', this.uploadForm.deviceCode)
          formData.append('remark', this.uploadForm.remark || '')

          uploadExcelFile(formData).then(response => {
            this.uploading = false
            if (response.code === 200) {
              this.$message.success('文件上传成功，正在解析中...')
              this.successVisible = true
              this.resetForm()
              this.getStatistics()
              this.getRecentUploads()
            } else {
              this.$message.error(response.msg || '上传失败')
            }
          }).catch(() => {
            this.uploading = false
            this.$message.error('上传失败')
          })
        }
      })
    },
    /** 重置表单 */
    resetForm() {
      this.uploadForm = {
        deviceCode: '',
        file: null,
        remark: ''
      }
      this.fileList = []
      this.$refs.uploadForm.resetFields()
      this.$refs.upload.clearFiles()
    },
    /** 查看详情 */
    viewDetail(id) {
      this.$router.push(`/detection/data/detail/${id}`)
    },
    /** 重新解析 */
    reparse(id) {
      this.$confirm('确认要重新解析该文件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        reparseExcelFile(id).then(response => {
          if (response.code === 200) {
            this.$message.success('重新解析已开始')
            this.getRecentUploads()
          } else {
            this.$message.error(response.msg || '重新解析失败')
          }
        })
      })
    },
    /** 获取状态类型 */
    getStatusType(status) {
      const statusMap = {
        0: 'warning',
        1: 'success',
        2: 'danger'
      }
      return statusMap[status] || 'info'
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        0: '待解析',
        1: '解析成功',
        2: '解析失败'
      }
      return statusMap[status] || '未知'
    },
    /** 上传成功处理 */
    handleUploadSuccess() {
      this.successVisible = true
    },
    /** 跳转到数据列表 */
    goToDataList() {
      this.successVisible = false
      this.$router.push('/detection/data/list')
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #124B9A, #0D438D);
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;

  h2 {
    color: #ffffff;
    margin: 0;
  }

  .page-description {
    color: rgba(255, 255, 255, 0.8);
    margin-top: 5px;
  }
}

.statistics-card {
  position: relative;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  ::v-deep .el-card__body {
    background: transparent;
  }

  .statistics-content {
    position: relative;
    z-index: 2;

    .statistics-number {
      font-size: 24px;
      font-weight: bold;
      color: #ffffff;
    }

    .statistics-label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      margin-top: 5px;
    }
  }

  .statistics-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 40px;
    opacity: 0.3;

    &.success {
      color: #67C23A;
    }

    &.warning {
      color: #E6A23C;
    }

    &.danger {
      color: #F56C6C;
    }
  }
}

.upload-card {
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  ::v-deep .el-card__header {
    background: rgba(13, 67, 141, 0.3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
  }

  ::v-deep .el-card__body {
    background: transparent;
  }

  // 表单标签样式
  ::v-deep .el-form-item__label {
    color: #ffffff;
    font-weight: 500;
  }

  // 输入框样式
  ::v-deep .el-input__inner {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 0.5);
    color: #333;
  }

  // 选择框样式
  ::v-deep .el-select .el-input__inner {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 0.5);
  }

  // 文本域样式
  ::v-deep .el-textarea__inner {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 0.5);
    color: #333;
  }

  .upload-demo {
    width: 500px;

    // 自定义上传拖拽区域样式
    ::v-deep .el-upload-dragger {
      background: linear-gradient(180deg, #124B9A, #0D438D);
      border: 2px dashed rgba(255, 255, 255, 0.3);
      border-radius: 8px;
      color: #ffffff;
      transition: all 0.3s ease;

      &:hover {
        border-color: rgba(255, 255, 255, 0.6);
        background: linear-gradient(180deg, #1557A8, #0F4A9B);
      }

      .el-icon-upload {
        color: #ffffff;
        font-size: 48px;
        margin-bottom: 16px;
      }

      .el-upload__text {
        color: #ffffff;
        font-size: 16px;

        em {
          color: #ffffff;
          font-weight: 600;
          text-decoration: underline;
        }
      }
    }

    // 上传提示文字样式
    ::v-deep .el-upload__tip {
      color: #ffffff;
      opacity: 0.8;
      margin-top: 8px;
    }
  }
}

.recent-uploads {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  ::v-deep .el-card__header {
    background: rgba(13, 67, 141, 0.3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
  }

  ::v-deep .el-card__body {
    background: transparent;
  }

  .el-table {
    margin-top: 10px;
    background: transparent;

    ::v-deep .el-table__header-wrapper {
      background: rgba(255, 255, 255, 0.1);
    }

    ::v-deep .el-table__header th {
      background: rgba(255, 255, 255, 0.1);
      color: #ffffff;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    ::v-deep .el-table__body tr {
      background: rgba(255, 255, 255, 0.05);

      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
    }

    ::v-deep .el-table__body td {
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      color: #ffffff;
    }

    ::v-deep .el-button--text {
      color: #ffffff;

      &:hover {
        color: #409EFF;
      }
    }
  }
}

.mb20 {
  margin-bottom: 20px;
}

.app-container {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .help-content {
    h4 {
      color: #333;
      margin: 20px 0 10px 0;

      &:first-child {
        margin-top: 0;
      }
    }

    ol, ul {
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        line-height: 1.5;
      }
    }

    .el-collapse {
      margin-top: 15px;
    }
  }

  .success-content {
    text-align: center;
    padding: 20px;

    i {
      display: block;
      margin-bottom: 15px;
    }

    p {
      margin: 10px 0;
      color: #333;

      &:first-of-type {
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
}

// 全局对话框样式
::v-deep .el-dialog {
  background: linear-gradient(180deg, #124B9A, #0D438D);
  border: 1px solid rgba(255, 255, 255, 0.2);

  .el-dialog__header {
    background: rgba(13, 67, 141, 0.3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);

    .el-dialog__title {
      color: #ffffff;
    }

    .el-dialog__close {
      color: #ffffff;
    }
  }

  .el-dialog__body {
    background: transparent;
    color: #ffffff;
  }
}
</style>
