<template>
  <el-dialog
    title="导入进度"
    :visible.sync="dialogVisible"
    width="900px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    :modal-append-to-body="false"
    top="5vh"
  >
    <div v-if="progressData" class="progress-container">
      <!-- 基本信息 -->
      <div class="basic-info">
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="任务ID">{{ progressData.taskId }}</el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag :type="getStatusType(progressData.status)">
              {{ getStatusText(progressData.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="成功导入">{{ progressData.importedDeviceCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="导入失败">{{ progressData.failedDeviceCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatDateTime(progressData.startTime) }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ formatDateTime(progressData.completeTime) }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 进度条 -->
      <div class="progress-section">
        <div class="progress-header">
          <span class="progress-title">导入进度</span>
          <span class="progress-percentage">{{ progressData.progress || 0 }}%</span>
        </div>
        <el-progress
          :percentage="progressData.progress || 0"
          :status="getProgressStatus(progressData.status)"
          :stroke-width="20"
        />
        <div class="current-step">
          <i class="el-icon-loading" v-if="progressData.status === 0"></i>
          {{ progressData.currentStep || '准备中...' }}
        </div>
      </div>

      <!-- 统计信息 -->
      <div v-if="progressData.statistics" class="statistics-section">
        <h4><i class="el-icon-data-analysis"></i> 导入统计</h4>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="el-icon-monitor"></i>
              </div>
              <div class="stat-value">{{ progressData.statistics.totalDeviceCount || 0 }}</div>
              <div class="stat-label">总设备数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="el-icon-s-order"></i>
              </div>
              <div class="stat-value">{{ progressData.statistics.deviceInfoImportCount || 0 }}</div>
              <div class="stat-label">设备信息</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="el-icon-connection"></i>
              </div>
              <div class="stat-value">{{ progressData.statistics.templateBindingImportCount || 0 }}</div>
              <div class="stat-label">模板绑定</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="el-icon-document-add"></i>
              </div>
              <div class="stat-value">{{ formatNumber(progressData.statistics.basicFieldImportCount) }}</div>
              <div class="stat-label">基础字段</div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="el-icon-s-data"></i>
              </div>
              <div class="stat-value">{{ formatNumber(progressData.statistics.tableDataRowImportCount) }}</div>
              <div class="stat-label">表格数据行</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="el-icon-document-copy"></i>
              </div>
              <div class="stat-value">{{ formatNumber(progressData.statistics.parseLogImportCount) }}</div>
              <div class="stat-label">解析日志</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item success-stat">
              <div class="stat-icon">
                <i class="el-icon-success"></i>
              </div>
              <div class="stat-value">{{ (progressData.statistics.successRate || 0).toFixed(1) }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 导入详情 -->
      <div v-if="progressData.importDetails && progressData.importDetails.length > 0" class="details-section">
        <h4>导入详情</h4>
        <div class="details-container">
          <el-table
            :data="progressData.importDetails"
            size="mini"
            max-height="200"
            style="width: 100%"
          >
            <el-table-column prop="index" label="序号" width="80" />
            <el-table-column prop="deviceCode" label="编码" width="120" />
            <el-table-column prop="dataType" label="数据类型" width="200" />
            <el-table-column prop="status" label="状态" width="80" align="center">
              <template slot-scope="scope">
                <el-tag :type="getDetailStatusType(scope.row.status)" size="mini">
                  {{ getDetailStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="message" label="处理消息" show-overflow-tooltip />
            <el-table-column prop="processTime" label="处理时间" width="200">
              <template slot-scope="scope">
                {{ formatDateTime(scope.row.processTime) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 成功/失败设备列表 -->
      <div v-if="isCompleted()" class="result-section">
        <h4><i class="el-icon-s-flag"></i> 导入结果</h4>
        <el-row :gutter="24">
          <el-col :span="12" v-if="progressData.successDeviceCodes && progressData.successDeviceCodes.length > 0">
            <div class="result-list success-list">
              <div class="result-header">
                <i class="el-icon-success"></i>
                <span>成功导入的设备 ({{ progressData.successDeviceCodes.length }})</span>
              </div>
              <div class="device-tags">
                <el-tag
                  v-for="deviceCode in progressData.successDeviceCodes"
                  :key="deviceCode"
                  type="success"
                  size="medium"
                  effect="light"
                  style="margin: 3px; font-weight: 500;"
                >
                  {{ deviceCode }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="12" v-if="progressData.failedDeviceCodes && progressData.failedDeviceCodes.length > 0">
            <div class="result-list failed-list">
              <div class="result-header">
                <i class="el-icon-error"></i>
                <span>导入失败的设备 ({{ progressData.failedDeviceCodes.length }})</span>
              </div>
              <div class="device-tags">
                <el-tag
                  v-for="deviceCode in progressData.failedDeviceCodes"
                  :key="deviceCode"
                  type="danger"
                  size="medium"
                  effect="light"
                  style="margin: 3px; font-weight: 500;"
                >
                  {{ deviceCode }}
                </el-tag>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 错误信息 -->
      <div v-if="isFailed()" class="error-section">
        <el-alert
          title="导入失败"
          :description="progressData.errorMessage"
          type="error"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <div v-else class="loading-container">
      <el-loading-spinner />
      <p>正在获取导入进度...</p>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button
        v-if="progressData && progressData.status === 0"
        type="danger"
        @click="cancelImport"
        :loading="cancelling"
      >
        取消导入
      </el-button>
      <el-button @click="closeDialog">
        {{ progressData && isCompleted() ? '关闭' : '后台运行' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getImportProgress, cancelDeviceImport } from '@/api/device/import'

export default {
  name: 'ImportProgressDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    taskId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      progressData: null,
      polling: null,
      cancelling: false
    }
  },
  watch: {
    visible(newVal) {
      this.dialogVisible = newVal
      if (newVal && this.taskId) {
        this.startPolling()
      } else {
        this.stopPolling()
      }
    },
    taskId(newVal) {
      if (newVal && this.visible) {
        this.startPolling()
      }
    },
    dialogVisible(newVal) {
      this.$emit('update:visible', newVal)
      if (!newVal) {
        this.stopPolling()
      }
    }
  },
  beforeDestroy() {
    this.stopPolling()
  },
  methods: {
    // 开始轮询进度
    startPolling() {
      this.stopPolling()
      this.getProgress()
      this.polling = setInterval(() => {
        this.getProgress()
      }, 2000) // 每2秒轮询一次
    },

    // 停止轮询
    stopPolling() {
      if (this.polling) {
        clearInterval(this.polling)
        this.polling = null
      }
    },

    // 获取进度
    async getProgress() {
      if (!this.taskId) return

      try {
        const response = await getImportProgress(this.taskId)
        if (response.code === 200) {
          this.progressData = response.data

          // 如果任务完成，停止轮询
          if (this.progressData && this.isCompleted()) {
            this.stopPolling()
          }
        }
      } catch (error) {
        console.error('获取导入进度失败:', error)
        this.stopPolling()
      }
    },

    // 取消导入
    async cancelImport() {
      try {
        await this.$confirm('确定要取消导入任务吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        this.cancelling = true
        const response = await cancelDeviceImport(this.taskId)
        if (response.code === 200) {
          this.$message.success('导入任务已取消')
          this.closeDialog()
        } else {
          this.$message.error(response.msg || '取消导入任务失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消导入失败:', error)
          this.$message.error('取消导入失败')
        }
      } finally {
        this.cancelling = false
      }
    },

    // 关闭对话框
    closeDialog() {
      this.dialogVisible = false
      this.$emit('close')
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'info',     // 进行中
        1: 'success',  // 成功
        2: 'danger',   // 失败
        3: 'warning'   // 部分成功
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '进行中',
        1: '成功',
        2: '失败',
        3: '部分成功'
      }
      return statusMap[status] || '未知'
    },

    // 获取进度条状态
    getProgressStatus(status) {
      if (status === 1) return 'success'
      if (status === 2) return 'exception'
      if (status === 3) return 'warning'
      return null
    },

    // 获取详情状态类型
    getDetailStatusType(status) {
      const statusMap = {
        'SUCCESS': 'success',
        'FAILED': 'danger',
        'SKIPPED': 'warning'
      }
      return statusMap[status] || 'info'
    },

    // 获取详情状态文本
    getDetailStatusText(status) {
      const statusMap = {
        'SUCCESS': '成功',
        'FAILED': '失败',
        'SKIPPED': '跳过'
      }
      return statusMap[status] || status
    },

    // 格式化数字
    formatNumber(num) {
      if (!num) return '0'
      return num.toLocaleString()
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString()
    },

    // 判断任务是否完成
    isCompleted() {
      return this.progressData && (this.progressData.status === 1 || this.progressData.status === 2 || this.progressData.status === 3)
    },

    // 判断任务是否失败
    isFailed() {
      return this.progressData && this.progressData.status === 2
    }
  }
}
</script>

<style scoped>
.progress-container {
  padding: 10px 20px;
  max-height: 80vh;
  overflow-y: auto;
  background: linear-gradient(180deg, #124B9A, #0D438D);
  border-radius: 4px;
}

::v-deep .el-dialog__body, .el-dialog--center .el-dialog__body {
  padding: 10px !important;
}
::v-deep .el-dialog__footer {
  padding: 10px !important;
}

.basic-info {
  margin-bottom: 20px;
  background: linear-gradient(180deg, #124B9A, #0D438D);
  padding: 15px;
  border-radius: 4px;
}

.progress-section {
  margin-bottom: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.progress-title {
  font-weight: bold;
  color: #ffffff;
}

.progress-percentage {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}

.current-step {
  margin-top: 10px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
}

.statistics-section {
  margin-bottom: 20px;
}

.details-section h4,
.statistics-section h4 {
  margin: 0 0 15px 0;
  color: #ffffff;
  font-weight: 600;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.stat-icon {
  font-size: 28px;
  margin-bottom: 10px;
  color: rgba(255, 255, 255, 0.9);
  display: block;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.success-stat {
  background: linear-gradient(135deg, #52c41a, #389e0d) !important;
}

.success-stat::before {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
}

.details-section {
  margin-bottom: 20px;
}

.details-section h4 {
  margin: 0 0 15px 0;
  color: #ffffff;
}

.details-container {
  max-height: 200px;
  overflow-y: auto;
}

.result-section {
  margin-bottom: 25px;
}

.result-list {
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.result-list:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.success-list {
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.1) 0%, rgba(56, 158, 13, 0.1) 100%);
  border: 1px solid rgba(82, 196, 26, 0.3);
}

.failed-list {
  background: linear-gradient(135deg, rgba(245, 108, 108, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
  border: 1px solid rgba(245, 108, 108, 0.3);
}

.result-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.result-header i {
  margin-right: 8px;
  font-size: 16px;
}

.result-header span {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.success-list .result-header i {
  color: #52c41a;
}

.failed-list .result-header i {
  color: #ff4d4f;
}

.device-tags {
  max-height: 120px;
  overflow-y: auto;
  padding-right: 5px;
}

.device-tags::-webkit-scrollbar {
  width: 4px;
}

.device-tags::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.error-section {
  margin-bottom: 20px;
}

.loading-container {
  text-align: center;
  padding: 40px 0;
  color: #606266;
}

.dialog-footer {
  text-align: right;
  padding: 10px;
  background: linear-gradient(180deg, #124B9A, #0D438D);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}
</style>
