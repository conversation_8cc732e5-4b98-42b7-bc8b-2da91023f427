<template>
  <div class="time-range-selector">
    <el-radio-group v-model="selectedType" @change="handleTypeChange">
      <el-radio label="FULL_YEAR">全年 (12个月)</el-radio>
      <el-radio label="HALF_YEAR">半年 (6个月)</el-radio>
      <el-radio label="QUARTER">季度 (3个月)</el-radio>
      <el-radio label="SINGLE_MONTH">单月 (1个月)</el-radio>
      <el-radio label="ALL_DATA">全部数据</el-radio>
      <el-radio label="CUSTOM">自定义</el-radio>
    </el-radio-group>
    
    <!-- 自定义时间范围 -->
    <div v-if="selectedType === 'CUSTOM'" class="custom-time-range">
      <el-date-picker
        v-model="customTimeRange"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="yyyy-MM-dd HH:mm:ss"
        value-format="yyyy-MM-dd HH:mm:ss"
        @change="handleCustomTimeChange"
        style="width: 100%; margin-top: 10px;"
      />
    </div>
    
    <!-- 时间范围预览 -->
    <div class="time-range-preview">
      <el-alert
        :title="getTimeRangeDescription()"
        type="info"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'TimeRangeSelector',
  props: {
    value: {
      type: String,
      default: 'QUARTER'
    },
    startTime: {
      type: String,
      default: null
    },
    endTime: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      selectedType: this.value,
      customTimeRange: this.startTime && this.endTime ? [this.startTime, this.endTime] : null
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.selectedType = newVal
      },
      immediate: true
    },
    startTime: {
      handler(newVal) {
        if (this.selectedType === 'CUSTOM' && newVal && this.endTime) {
          this.customTimeRange = [newVal, this.endTime]
        }
      }
    },
    endTime: {
      handler(newVal) {
        if (this.selectedType === 'CUSTOM' && this.startTime && newVal) {
          this.customTimeRange = [this.startTime, newVal]
        }
      }
    }
  },
  methods: {
    // 处理类型变化
    handleTypeChange(type) {
      this.selectedType = type
      this.$emit('input', type)
      
      if (type !== 'CUSTOM') {
        // 清空自定义时间
        this.customTimeRange = null
        this.$emit('update:startTime', null)
        this.$emit('update:endTime', null)
      } else {
        // 设置默认的自定义时间范围（最近一个月）
        const now = new Date()
        const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        this.customTimeRange = [
          this.formatDateTime(oneMonthAgo),
          this.formatDateTime(now)
        ]
        this.handleCustomTimeChange(this.customTimeRange)
      }
    },
    
    // 处理自定义时间变化
    handleCustomTimeChange(timeRange) {
      if (timeRange && timeRange.length === 2) {
        this.$emit('update:startTime', timeRange[0])
        this.$emit('update:endTime', timeRange[1])
      } else {
        this.$emit('update:startTime', null)
        this.$emit('update:endTime', null)
      }
    },
    
    // 获取时间范围描述
    getTimeRangeDescription() {
      const now = new Date()
      let startTime, endTime
      
      switch (this.selectedType) {
        case 'FULL_YEAR':
          startTime = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          endTime = now
          return `时间范围：${this.formatDate(startTime)} 至 ${this.formatDate(endTime)} (最近12个月)`
          
        case 'HALF_YEAR':
          startTime = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000)
          endTime = now
          return `时间范围：${this.formatDate(startTime)} 至 ${this.formatDate(endTime)} (最近6个月)`
          
        case 'QUARTER':
          startTime = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          endTime = now
          return `时间范围：${this.formatDate(startTime)} 至 ${this.formatDate(endTime)} (最近3个月)`
          
        case 'SINGLE_MONTH':
          startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          endTime = now
          return `时间范围：${this.formatDate(startTime)} 至 ${this.formatDate(endTime)} (最近1个月)`
          
        case 'ALL_DATA':
          return '时间范围：全部数据 (不限制时间范围)'
          
        case 'CUSTOM':
          if (this.customTimeRange && this.customTimeRange.length === 2) {
            return `时间范围：${this.customTimeRange[0]} 至 ${this.customTimeRange[1]} (自定义)`
          } else {
            return '请选择自定义时间范围'
          }
          
        default:
          return '请选择时间范围'
      }
    },
    
    // 格式化日期
    formatDate(date) {
      return date.toLocaleDateString('zh-CN')
    },
    
    // 格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }
  }
}
</script>

<style scoped>
.time-range-selector {
  width: 100%;
}

.custom-time-range {
  margin-top: 10px;
}

.time-range-preview {
  margin-top: 15px;
}

.el-radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.el-radio {
  margin-right: 0;
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .el-radio-group {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
}

@media (min-width: 1024px) {
  .el-radio-group {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }
}
</style>
