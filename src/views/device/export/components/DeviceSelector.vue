<template>
  <div class="device-selector">
    <!-- 全部设备选择 -->
    <div class="select-all-section" v-if="multiple">
      <el-checkbox
        v-model="selectAll"
        @change="handleSelectAllChange"
        :indeterminate="isIndeterminate"
      >
        全部设备 ({{ allDevices.length }})
      </el-checkbox>
    </div>

    <el-select
      v-model="selectedDevices"
      :multiple="multiple"
      :placeholder="placeholder"
      filterable
      remote
      reserve-keyword
      :remote-method="searchDevices"
      :loading="loading"
      style="width: 100%"
      @change="handleChange"
      :disabled="selectAll && multiple"
    >
      <el-option
        v-for="device in deviceOptions"
        :key="device.value"
        :label="device.label"
        :value="device.value"
      >
        <span style="float: left">{{ device.label }}</span>
        <span style="float: right; color: #8492a6; font-size: 13px">{{ device.value }}</span>
      </el-option>
    </el-select>

    <!-- 已选设备显示 -->
    <div v-if="multiple && selectedDevices.length > 0 && !selectAll" class="selected-devices">
      <div class="selected-header">
        <span>已选设备 ({{ selectedDevices.length }})</span>
        <el-button type="text" size="mini" @click="clearAll">清空</el-button>
      </div>
      <div class="selected-list">
        <el-tag
          v-for="deviceCode in selectedDevices"
          :key="deviceCode"
          closable
          @close="removeDevice(deviceCode)"
          style="margin: 2px;"
        >
          {{ getDeviceLabel(deviceCode) }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script>
import { getBindOptions } from '@/api/analysis/deviceTemplateBinding'
import {getAllDevice} from "@/api/analysis/device";

export default {
  name: 'DeviceSelector',
  props: {
    value: {
      type: [String, Array],
      default: () => []
    },
    multiple: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择设备'
    },
    // 是否只显示已绑定模板的设备
    onlyBound: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      selectedDevices: this.multiple ? (Array.isArray(this.value) ? [...this.value] : []) : this.value,
      deviceOptions: [],
      allDevices: [],
      loading: false,
      searchKeyword: '',
      selectAll: false,
      isIndeterminate: false
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal === 'ALL_DEVICES') {
          // 如果传入的是全选标识，设置全选状态
          this.selectAll = true
          this.selectedDevices = this.allDevices.map(device => device.value)
        } else {
          this.selectedDevices = this.multiple ? (Array.isArray(newVal) ? [...newVal] : []) : newVal
          this.updateSelectAllState()
        }
      },
      immediate: true
    },
    selectedDevices: {
      handler() {
        this.updateSelectAllState()
      }
    }
  },
  mounted() {
    this.loadDevices()
  },
  methods: {
    // 加载设备列表
    async loadDevices() {
      try {
        this.loading = true
        let response

        if (this.onlyBound) {
          // 获取已绑定模板的设备
          response = await getBindOptions()
        } else {
          // 获取所有设备
          response = await getAllDevice()
        }

        if (response.code === 200) {
          this.allDevices = response.data.map(item => ({
            value: item.value,
            label: item.label
          }))
          this.deviceOptions = [...this.allDevices]

          // 如果当前值是全选，需要更新选中的设备列表
          if (this.value === 'ALL_DEVICES') {
            this.selectAll = true
            this.selectedDevices = this.allDevices.map(device => device.value)
          } else {
            this.updateSelectAllState()
          }
        }
      } catch (error) {
        console.error('加载设备列表失败:', error)
        this.$message.error('加载设备列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索设备
    searchDevices(keyword) {
      this.searchKeyword = keyword
      if (keyword) {
        this.deviceOptions = this.allDevices.filter(device =>
          device.label.toLowerCase().includes(keyword.toLowerCase()) ||
          device.value.toLowerCase().includes(keyword.toLowerCase())
        )
      } else {
        this.deviceOptions = [...this.allDevices]
      }
    },

    // 处理选择变化
    handleChange(value) {
      this.selectedDevices = value
      this.updateSelectAllState()
      this.$emit('input', this.getEmitValue())
      this.$emit('change', this.getEmitValue())
    },

    // 处理全选变化
    handleSelectAllChange(value) {
      if (value) {
        // 选择全部设备
        this.selectedDevices = this.allDevices.map(device => device.value)
      } else {
        // 取消全选
        this.selectedDevices = []
      }
      this.$emit('input', this.getEmitValue())
      this.$emit('change', this.getEmitValue())
    },

    // 更新全选状态
    updateSelectAllState() {
      if (!this.multiple || this.allDevices.length === 0) {
        return
      }

      const selectedCount = this.selectedDevices.length
      const totalCount = this.allDevices.length

      if (selectedCount === 0) {
        this.selectAll = false
        this.isIndeterminate = false
      } else if (selectedCount === totalCount) {
        this.selectAll = true
        this.isIndeterminate = false
      } else {
        this.selectAll = false
        this.isIndeterminate = true
      }
    },

    // 获取要发送的值
    getEmitValue() {
      if (this.selectAll && this.multiple) {
        return 'ALL_DEVICES' // 特殊标识表示选择全部设备
      }
      return this.selectedDevices
    },

    // 移除设备
    removeDevice(deviceCode) {
      if (this.multiple) {
        const index = this.selectedDevices.indexOf(deviceCode)
        if (index > -1) {
          this.selectedDevices.splice(index, 1)
          this.handleChange(this.selectedDevices)
        }
      }
    },

    // 清空所有选择
    clearAll() {
      this.selectedDevices = []
      this.selectAll = false
      this.$emit('input', this.getEmitValue())
      this.$emit('change', this.getEmitValue())
    },

    // 获取设备标签
    getDeviceLabel(deviceCode) {
      const device = this.allDevices.find(d => d.value === deviceCode)
      return device ? device.label : deviceCode
    }
  }
}
</script>

<style scoped>
.device-selector {
  width: 100%;
}

.select-all-section {
  margin-bottom: 10px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
}

.select-all-section .el-checkbox {
  font-weight: 500;
  color: #409eff;
}

.selected-devices {
  margin-top: 10px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.selected-list {
  max-height: 120px;
  overflow-y: auto;
}

.selected-list::-webkit-scrollbar {
  width: 6px;
}

.selected-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.selected-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.selected-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
