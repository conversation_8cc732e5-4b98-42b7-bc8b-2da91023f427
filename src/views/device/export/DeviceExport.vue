<template>
  <div class="device-export-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span class="card-title">设备数据导出</span>
      </div>

      <el-form ref="exportForm" :model="exportForm" :rules="exportRules" label-width="120px">
        <!-- 设备选择 -->
        <el-form-item label="选择设备" prop="deviceCodes">
          <device-selector
            v-model="exportForm.deviceCodes"
            :multiple="true"
            :only-bound="false"
            placeholder="请选择要导出的设备"
          />
        </el-form-item>

        <!-- 时间范围选择 -->
        <el-form-item label="时间范围" prop="timeRangeType">
          <time-range-selector
            v-model="exportForm.timeRangeType"
            :start-time.sync="exportForm.startTime"
            :end-time.sync="exportForm.endTime"
          />
        </el-form-item>

        <!-- 导出选项 -->
        <el-form-item label="导出内容">
          <el-checkbox-group v-model="exportOptions">
            <el-checkbox label="includeRawFiles">原始文件</el-checkbox>
            <el-checkbox label="includeParsedData">解析数据</el-checkbox>
            <el-checkbox label="includeTemplateInfo">模板信息</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 备注 -->
        <el-form-item label="备注">
          <el-input
            v-model="exportForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入导出备注"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="startExport" :loading="exporting">
            <i class="el-icon-download"></i>
            开始导出
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 导出进度对话框 -->
    <export-progress-dialog
      :visible.sync="progressVisible"
      :task-id="currentTaskId"
      @download="handleDownload"
      @close="handleProgressClose"
    />


  </div>
</template>

<script>
import DeviceSelector from './components/DeviceSelector'
import TimeRangeSelector from './components/TimeRangeSelector'
import ExportProgressDialog from './components/ExportProgressDialog'
import { startDeviceExport, getExportProgress, cancelDeviceExport, downloadExportFile } from '@/api/device/export'

export default {
  name: 'DeviceExport',
  components: {
    DeviceSelector,
    TimeRangeSelector,
    ExportProgressDialog
  },
  data() {
    return {
      // 导出表单
      exportForm: {
        deviceCodes: [],
        timeRangeType: 'QUARTER',
        startTime: null,
        endTime: null,
        remark: ''
      },
      // 导出选项
      exportOptions: ['includeRawFiles', 'includeParsedData', 'includeTemplateInfo'],
      // 表单验证规则
      exportRules: {
        deviceCodes: [
          { required: true, message: '请选择要导出的设备', trigger: 'change' }
        ],
        timeRangeType: [
          { required: true, message: '请选择时间范围', trigger: 'change' }
        ]
      },
      // 导出状态
      exporting: false,
      progressVisible: false,
      currentTaskId: null
    }
  },
  mounted() {
    // 页面初始化
  },
  methods: {
    // 开始导出
    async startExport() {
      try {
        await this.$refs.exportForm.validate()

        // 检查设备选择
        if (!this.exportForm.deviceCodes ||
            (Array.isArray(this.exportForm.deviceCodes) && this.exportForm.deviceCodes.length === 0) ||
            this.exportForm.deviceCodes === 'ALL_DEVICES') {
          // 如果是全部设备或者没有选择设备，需要特殊处理
          if (this.exportForm.deviceCodes !== 'ALL_DEVICES') {
            this.$message.error('请选择要导出的设备')
            return
          }
        }

        this.exporting = true

        const exportRequest = {
          ...this.exportForm,
          includeRawFiles: this.exportOptions.includes('includeRawFiles'),
          includeParsedData: this.exportOptions.includes('includeParsedData'),
          includeTemplateInfo: this.exportOptions.includes('includeTemplateInfo')
        }

        const response = await startDeviceExport(exportRequest)
        if (response.code === 200) {
          this.currentTaskId = response.data
          this.progressVisible = true
          this.$message.success('导出任务已启动')
          this.getExportHistory() // 刷新历史记录
        } else {
          this.$message.error(response.msg || '启动导出任务失败')
        }
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败: ' + (error.message || '未知错误'))
      } finally {
        this.exporting = false
      }
    },

    // 重置表单
    resetForm() {
      this.$refs.exportForm.resetFields()
      this.exportOptions = ['includeRawFiles', 'includeParsedData', 'includeTemplateInfo']
    },

    // 处理下载
    handleDownload(taskId) {
      this.downloadFile({ taskId })
    },

    // 处理进度对话框关闭
    handleProgressClose() {
      this.progressVisible = false
      this.currentTaskId = null
    },



    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '-'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return `${size.toFixed(2)} ${units[index]}`
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'info',    // 进行中
        1: 'success', // 成功
        2: 'danger'   // 失败
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '进行中',
        1: '成功',
        2: '失败'
      }
      return statusMap[status] || '未知'
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString()
    }
  }
}
</script>

<style scoped>
.device-export-container {
  padding: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
}

.box-card {
  margin-bottom: 20px;
}
</style>
