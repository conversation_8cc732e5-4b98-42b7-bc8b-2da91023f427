<template>
  <div class="container">
    <div class="top d-flex j-sb a-center">
      <img src="@/assets/favicon.png" style="width: 120px; height: 80px" />
      <dv-border-box-8 style="width: 300px">
        <span>7451数据采集平台</span>
      </dv-border-box-8>
      <div class="d-flex">
        <el-button
          type="primary"
          v-if="item.isCamera && !historyItem"
          @click="getVideo"
          >视频监控</el-button
        >
        <el-button type="primary" v-if="!historyItem" @click="toVnc"
          >VNC连接</el-button
        >
        <el-button
          type="primary"
          @click="historyItem ? (historyItem = null) : $router.go(-1)"
          >返回</el-button
        >
      </div>
    </div>
    <div class="bottom" v-if="!historyItem">
      <div style="width: 30%; height: 100%">
        <dv-border-box-12  style="width: 100%; height: 49.5%; margin-bottom: 1%">
          <p class="title">试验台信息</p>
          <div
            style="height: calc(100% - 30px)"
            class="d-flex flex-column j-center"
          >
            <el-descriptions :column="1" border>
              <el-descriptions-item
                label="设备名称"
                label-class-name="d"
                content-class-name="d"
                >{{ item.deviceName }}</el-descriptions-item
              >
              <el-descriptions-item
                label="设备编号"
                label-class-name="s"
                content-class-name="s"
                >{{ item.deviceCode }}</el-descriptions-item
              >
              <el-descriptions-item
                label="设备型号"
                label-class-name="d"
                content-class-name="d"
                >{{ item.deviceMode }}</el-descriptions-item
              >
              <el-descriptions-item
                label="设备状态"
                label-class-name="s"
                content-class-name="s"
              >
                <div class="d-flex a-center">
                  <div
                    class="status_light"
                    :class="[item.status == '在线' ? 's2' : 's1']"
                  ></div>
                  <div>{{ item.status }}</div>
                </div>
              </el-descriptions-item>
              <el-descriptions-item
                label="设备有效期"
                label-class-name="d"
                content-class-name="d"
                >{{ item.deviceDate }}</el-descriptions-item
              >
            </el-descriptions>
          </div>
        </dv-border-box-12>
        <dv-border-box-12 style="width: 100%; height: 49.5%">
          <p class="title">设备监控</p>
          <div style="height: calc(100% - 30px); width: 100%">
            <div class="table">
              <div class="item" style="background: #0a73ff">
                <div>序号</div>
                <div>使用时间</div>
                <div>结束时间</div>
              </div>
              <div class="list">
                <div class="item" v-for="(item,index) in useRecordList" :key="item.id">
                  <div>{{ index + 1 }}</div>
                  <div>{{ item.onlineTime }}</div>
                  <div>{{ item.offlineTime }}</div>
                </div>
              </div>
            </div>
          </div>
        </dv-border-box-12>
      </div>
      <div style="width: 40%; height: 100%">
        <dv-border-box-12 style="width: 100%; height: 49.5%; margin-bottom: 1%">
          <p class="title mr-b3">{{ item.deviceName }}</p>
          <img
            v-if="item.pathName"
            :src="$store.state.app.uploadUrl + item.pathName"
            style="width: 100%; height: calc(100% - 30px)"
          />
        </dv-border-box-12>
        <dv-border-box-12 style="width: 100%; height: 49.5%">
          <p class="title">数据采集</p>
          <div class="table">
            <div class="item" style="background: #0a73ff">
              <div>检测时间</div>
              <div>车辆编号</div>
              <div>检测人</div>
<!--              <div>是否合格</div>-->
            </div>
            <div class="list">
              <div
                class="item"
                @click="rowClick(item)"
                v-for="item in historyList"
                :key="item.id"
              >
                <div>{{ item.checkTime }}</div>
                <div>{{ item.equipCode }}</div>
                <div>{{ item.operator }}</div>
<!--                <div>{{ item.isSuccess }}</div>-->
              </div>
            </div>
            <lt-pagination
              :total="total"
              :page.sync="current"
              :limit.sync="pageSize"
              :auto="false"
              layout="total,prev, pager, next"
              @pagination="getHistoryList"
            />
          </div>
        </dv-border-box-12>
      </div>
      <div style="width: 30%; height: 100%">
        <dv-border-box-12 style="width: 100%; height: 49.5%; margin-bottom: 1%">
          <p class="title">设备介绍</p>
          <div
            style="
              height: calc(100% - 50px);
              margin-top: 20px;
              overflow: auto;
              text-indent: 2em;
            "
          >
            {{ item.deviceRemark }}
          </div>
        </dv-border-box-12>
        <dv-border-box-12 style="width: 100%; height: 49.5%">
          <p class="title">设备保养记录</p>
          <div class="table">
            <div class="item" style="background: #0a73ff">
              <div>序号</div>
              <div>保养日期</div>
              <div>保养人</div>
              <div>备注</div>
            </div>
            <div class="list">
              <div
                class="item"
                v-for="(item,index) in maintenanceRecord"
                :key="item.id"
              >
                <div>{{ index + 1 }}</div>
                <div>{{ item.maintenance_date }}</div>
                <div>{{ item.maintenance_user }}</div>
                <div>{{ item.remark }}</div>
                <!--                <div>{{ item.isSuccess }}</div>-->
              </div>
            </div>
            <lt-pagination
              :total="maintenanceTotal"
              :page.sync="maintenanceCurrent"
              :limit.sync="maintenancePageSize"
              :auto="false"
              layout="total,prev, pager, next"
              @pagination="getDeviceMaintenance"
            />
          </div>
        </dv-border-box-12>
      </div>
    </div>
    <div v-else class="bottom" style="display: block">

      <div v-if="historyItem.type == 1">
<!--        <dv-border-box-12 style="width: 100%; height: 30%">
          <p class="title">设备信息</p>
          <div style="height: calc(100% - 30px); width: 50%" class="d-flex flex-column j-center">
            <el-descriptions :column="4" border>

              <el-descriptions-item
                label-class-name="d"
                content-class-name="s"
                label="车辆编号"
              >
                {{ historyItem.carCode }}
              </el-descriptions-item>
              <el-descriptions-item
                label-class-name="d"
                content-class-name="s"
                label="装备型号"
              >
                {{ historyItem.carTypeName }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </dv-border-box-12>-->
        <dv-border-box-12 style="width: 100%;  margin-top: 1%">
          <p class="title">采集数据</p>
          <div style=" width: 100%">
            <el-table
              :data="filterData"
              style="width: 100%;"
              max-height="500px"
            >
              <el-table-column
                type="index"
                label="序号"
                width="100"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="date"
                label="时间"
                width="200"
                align="center"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="introduce"
                label="介绍"
                width="700"
                align="center"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="FinalTorque"
                label="力矩(N·m)"
                align="center"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="FinalAngle"
                label="角度(°)"
                align="center"
              ></el-table-column>
            </el-table>

          </div>


        </dv-border-box-12>
      </div>
      <div v-else>
        <dv-border-box-12 style="width: 100%; height: 30%">
          <p class="title">设备信息</p>
          <div style="height: calc(100% - 30px); width: 100%" class="d-flex flex-column j-center">
            <el-descriptions :column="4" border>
              <el-descriptions-item
                label-class-name="d"
                content-class-name="s"
                label="装备型号"
              >
                {{ historyItem.carTypeName }}
              </el-descriptions-item>
              <el-descriptions-item
                label-class-name="d"
                content-class-name="s"
                label="检查人员"
              >
                {{ historyItem.checkUser }}
              </el-descriptions-item>
              <el-descriptions-item
                label-class-name="d"
                content-class-name="s"
                label="车辆编号"
              >
                {{ historyItem.carCode }}
              </el-descriptions-item>
              <el-descriptions-item
                label-class-name="d"
                content-class-name="s"
                label="检查时间"
              >
                {{ historyItem.checkTime }}
              </el-descriptions-item>
              <el-descriptions-item
                label-class-name="d"
                content-class-name="s"
                label="修理类型"
              >
                {{ historyItem.repairTypeName }}
              </el-descriptions-item>
              <el-descriptions-item
                label-class-name="d"
                content-class-name="s"
                label="产品编号"
              >
                {{ historyItem.productCode }}
              </el-descriptions-item>
              <!--            <el-descriptions-item
                            label-class-name="d"
                            content-class-name="s"
                            label="入场时间"
                          >
                            {{ historyItem.inTime }}
                          </el-descriptions-item>-->
              <el-descriptions-item
                label-class-name="d"
                content-class-name="s"
                label="备注"
              >
                {{ historyItem.remark }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </dv-border-box-12>
        <dv-border-box-12 style="width: 100%;  margin-top: 1%">
          <p class="title">采集数据</p>
          <div style=" width: 100%">
            <el-table
              :data="historyItem.data"
              style="width: 100%;"
              max-height="500px"
            >
              <el-table-column
                type="index"
                label="序号"
                width="100"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="item"
                label="项目"
                width="300"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="item_explain"
                label="说明"
                width="500"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                v-if="this.item.showMin == 1"
                prop="check_min"
                label="最小值"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                v-if="this.item.showMin == 1"
                prop="check_max"
                label="最大值"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="data"
                label="数据"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="result"
                label="结果"
                width="200"
                align="center"
              ></el-table-column>
            </el-table>

          </div>
        </dv-border-box-12>
        <div style="width: 100%">
          <div
            v-for="(item, index) in historyItem.picList"
            :key="index"
          >
            <el-image
              style="width: 100%"
              :src="$store.state.app.uploadUrl + item.file_path"
              fit="fill"></el-image>
          </div>
        </div>
      </div>


    </div>

    <el-dialog
      :visible="visible"
      title="vnc页面"
      width="100%"
      @close="visible = false"
    >
      <iframe
        :src="item.vncIp"
        border="none"
        style="height: 85vh; width: 100%; overflow: auto"
      ></iframe>
    </el-dialog>
    <el-dialog
      :visible="show"
      title="视频监控"
      width="100%"
      @close="show = false"
    >
      <iframe
        src="http://www.7451.com/hk/index.html"
        style="height: 85vh; width: 100%; border: none"
        ref="video"
      ></iframe>
    </el-dialog>

    <el-dialog
      :visible="checkShow"
      :title="title"
      @close="checkShow = false"
      width="95%"
      append-to-body
    >
      <v-form-render
        v-if="formJson"
        ref="vFormRefLast"
        :form-json="formJson"
        :form-data="initData"
        :writeState="false"
        :key="randomKey"
      />
    </el-dialog>

  </div>
</template>

<script>
import ltPagination from "../../components/lt-pagination/lt-pagination.vue";
import { getByTemplateIdInfo } from '@/api/tool/form'
export default {
  components: { ltPagination },
  data() {
    return {
      info: null,
      total: 0,
      current: 1,
      pageSize: 10,
      maintenanceTotal: 0,
      maintenanceCurrent: 1,
      maintenancePageSize: 10,
      item: {},
      tableData: [],
      useRecordList: [],
      historyList: [],
      maintenanceRecord: [],
      visible: false,
      show: false,
      historyVisible: false,
      historyItem: null,
      filterData: [],
      title: '',
      randomKey: '',
      formJson: '',
      initData: {},
      keyToMap: {},
      aliasToMap: {},
      checkShow: false,
    };
  },
  methods: {
    toVnc(){
      window.open('/vnc.html',this.item.vncIp)
    },
    getVideo() {
      window.open(
        "/hk",
        JSON.stringify({
          ip: this.item.cameraIp || "************",
          port: this.item.cameraPort || "80",
          username: this.item.cameraUser || "admin",
          password: this.item.cameraPassword || "nljxc7451",
        })
      );
    },
    rowClick(row) {

      if (this.item.templateId != null && this.item.templateId !== '') {
        this.getFormJsonDialog(this.item.templateId, this.item.title, {
          carCode: row.carCode,
          teamId: this.item.teamId
        })
        return;
      }
      this.$api({
        url: "/interfaces/collect/save/getHistoryDetail",
        params: {
          carCode: row.carCode,
          dataSaveId: row.id,
          type: row.type
        },
      }).then((res) => {
        this.historyItem = res.data;
        if (this.historyItem.type == 1) {
          this.filterData = this.historyItem.data
            .filter(item => (item.FinalTorque >= 230 && item.FinalTorque <= 250) | (item.FinalTorque >= 450 && item.FinalTorque <= 470))
            .map(item => ({
              date: item.date,
              FinalAngle: item.FinalAngle,
              FinalTorque: item.FinalTorque,
              introduce: this.getIntroduce(item.FinalTorque)
            }))
        }
        // this.historyVisible = true;
      });
      // this.$ltKKFilePreview.open(row.fileId, 1200);
    },
    getIntroduce(torque){
      if (torque <= 250){
        return "侧减速器固定螺栓WZ212.16.007和M16×35的拧紧力矩应为240N·m左右."
      }else if (torque >= 450) {
        return "主动轮螺栓20×1.5x70-10.9-Z/C GB5786的拧紧力矩应为460N·m左右."
      }
      return ""
    },
    getUseRecordList() {
      this.$api({
        url: "/interfaces/collect/device/deviceUseRecord",
        params: {
          deviceCode: this.item.deviceCode,
        },
      }).then((res) => {
        this.useRecordList = res.data;
      });
    },
    getHistoryList() {
      this.$api({
        url: "/interfaces/collect/history/getTabelByDeviceId",
        params: {
          deviceId: this.item.id,
          pageNum: this.current,
          pageSize: this.pageSize,
        },
      }).then((res) => {
        this.historyList = res.data.records;
        this.total = res.data.total;
      });
    },
    getDeviceMaintenance() {
      this.$api({
        url: "/interfaces/collect/device/getDeviceMaintenanceRecord",
        params: {
          deviceId: this.item.id,
          pageNum: this.maintenanceCurrent,
          pageSize: this.maintenancePageSize,
        },
      }).then((res) => {
        this.maintenanceRecord = res.data.records;
        this.maintenanceTotal = res.data.total;
      });
    },
    getFormJsonDialog(templateId, title, param) {
      this.title = title
      getByTemplateIdInfo(templateId).then((res) => {
        this.checkShow = true
        var func = (templateJson) => {
          // 迭代出别名和elementkey
          let cr = (arr) => {
            arr.forEach((item) => {
              // 如果有别名 设置别名
              if (item.options.alias) {
                this.keyToMap[item.options.alias] = item.id
                this.aliasToMap[item.id] = item.options.alias
              }
              if (item.cols && item.cols.length > 0) {
                cr(item.cols)
              }
              if (item.widgetList && item.widgetList.length > 0) {
                cr(item.widgetList)
              }
            })
          }
          //迭代
          cr(templateJson.widgetList)

          Object.getOwnPropertyNames(this.initData)
            .filter((item) => item != '__ob__')
            .forEach((name) => {
              if (
                name.includes('subform') ||
                (this.keyToMap[name] && this.keyToMap[name].includes('subform'))
              ) {
                this.initData[name].forEach((it) => {
                  for (let k in it) {
                    if (this.keyToMap[k]) {
                      it[this.keyToMap[k]] = it[k]
                      delete it[k]
                    }
                  }
                })
              }
              if (this.keyToMap[name]) {
                this.initData[this.keyToMap[name]] = this.initData[name]
                delete this.initData[name]
              }
            })
          this.formJson = templateJson
        }
        this.initData = param

        if (res.data.templateJson) {
          let templateJson = JSON.parse(res.data.templateJson)
          func(templateJson)
          this.$nextTick(() => {
            this.randomKey = Math.random()
          })
        }
      })
    },
  },
  mounted() {
    let item = this.$route.params.item;
    this.item = item;
    this.getUseRecordList();
    this.getHistoryList();
    this.getDeviceMaintenance();
  },
};
</script>

<style lang="scss" scoped>
.container {
  background: #041a40;
  padding: 5px 15px;
  width: 100%;
  height: 100%;
  color: #fff;
  .top {
    height: 10%;
    span {
      color: #fff;
      font-size: 26px;
      font-weight: 600;
    }
    ::v-deep .dv-border-box-8 .border-box-content {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .bottom {
    margin-top: 5px;
    height: 89%;
    width: 100%;
    display: flex;
    .table {
      width: 100%;
      color: #fff;
      height: calc(100% - 30px);
      margin-top: 5px;
      display: flex;
      flex-direction: column;
      font-size: 14px;
      .item {
        width: 100%;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 5px 0;
        &:nth-of-type(odd) {
          background: #082c61;
        }
        &:nth-of-type(even) {
          background: #13487f;
        }
        & > div {
          flex: 1;
          text-align: center;
        }
      }

      .list {
        flex: 1;
        overflow: auto;
      }
    }
  }
}
::v-deep .border-box-content {
  padding: 12px;
}
p {
  text-align: center;
  &.title {
    color: #7ce7fd;
    font-size: 20px;
    margin: 0;
  }
}
::v-deep .d {
  background: #082c61;
  text-align: center;
  color: #fff;
}

::v-deep .s {
  color: #fff;
  background: #13487f;
  text-align: center;
}
::v-deep .echarts {
  width: 100%;
  height: 100%;
}
::v-deep .el-pagination span {
  color: #fff;
}

.status_light {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 10px;
}
.s1 {
  background-color: #ff4340;
  box-shadow: 0px 0px 10px #ff4340;
}
.s2 {
  background-color: #59cb33;
  box-shadow: 0px 0px 10px #59cb33;
}
</style>
