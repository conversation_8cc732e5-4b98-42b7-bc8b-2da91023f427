<template>
  <div class="container">
    <div class="d-flex a-center j-center top">
      <img src="./top.png" style="width: 100%; height: 100%" />
      <p>数字化修理线车辆采集信息</p>
    </div>

    <div style="flex: 1; overflow: auto" class="d-flex flex-wrap mr-t10">
      <div
        v-for="item in list"
        :key="item.car_code"
        class="item"
        @click="choose(item)"
      >
            {{item.car_code}}
      </div>
    </div>
    <div class="d-flex a-center j-center top">
      <img src="./bottom.png" style="width: 100%; height: 100%" />
      <p class="pd-t15 font-20">南岭机械厂</p>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [],
    };
  },
  methods: {
    choose(item) {
      this.$router.push({
        name: "CarDetail",
        params: { item },
      });
    },
  },
  mounted() {
    this.$api({
      url: "/interfaces/collect/car/getAllCar",
    }).then((res) => {
      this.list = res.data;
    });
  },
};
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .item {
    width: 19%;
    margin: 0 .5% 5px 0.5%;
    height: 150px;
    background: #0d5fb8;
    border-radius: 8px;
    color: #fefefe;
    font-size: 24px;
    font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  .top{
        width:100%;height:70px;position: relative;
        p{
            position: absolute;
            margin: 0;
            line-height: 50px;
            top: 0;
            left: 50%;
            transform: translate(-50%);

font-size: 28px;
font-family: Microsoft YaHei;
font-weight: bold;
color: #FFFFFF;
background: linear-gradient(0deg, #CFF1FF 0%, #FFFFFF 100%);
-webkit-background-clip: text;
-webkit-text-fill-color: transparent;
        }
    }
}
</style>
