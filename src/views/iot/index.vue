<template>
   <div class="container">
    <div class="d-flex a-center j-center top">
        <img src="./top.png" style="width:100%;height:100%"/>
        <p >数字化修理线数据智能采析平台</p>
    </div>

       <div style="flex:1;overflow:auto;" class="d-flex flex-wrap">
        <div v-for="item in list" :key="item.id" class="item" @click="choose(item)">
            <div class="d-flex j-end">
                <el-tag  style="transform:translate(50%,50%);z-index:999"  :type="item.status=='离线'?'danger':'success'">{{item.status}}</el-tag>
            </div>

            <img style="width:100%;height:100%;border-radius:5px;cursor:pointer" :src="$store.state.app.uploadUrl+item.pathName" />
            <p style="font-size:18px;text-align:center;margin-top:5px;font-weight:bold">{{item.deviceName}}</p>
        </div>


       </div>
      <div class="d-flex a-center j-center top">
        <img src="./bottom.png" style="width:100%;height:100%"/>
        <p class="pd-t15 font-20">南岭机械厂</p>
    </div>
   </div>
</template>

<script>
export default {
    data(){
        return{
            list:[]
        }
    },
    methods:{
        choose(item){
        //    window.open(item.bigscreenUrl,'_blank')
            this.$router.push(
                {
                    name:'IotDetail',
                    params:{item}
                })
        }
    },
    mounted(){
        this.$api({
            url:"/interfaces/collect/device/getDevice",
            params:{
                type:this.$route.path.split('/').at(-1)
            }
        }).then(res=>{
            this.list=res.data
        })
    }
}
</script>

<style lang="scss" scoped>
.container{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    .item{
        width: 17%;
        margin: 0 2.5% 60px .5%;
        height: 200px;
    }
    .top{
        width:100%;height:70px;position: relative;
        p{
            position: absolute;
            margin: 0;
            line-height: 50px;
            top: 0;
            left: 50%;
            transform: translate(-50%);

font-size: 28px;
font-family: Microsoft YaHei;
font-weight: bold;
color: #FFFFFF;
background: linear-gradient(0deg, #CFF1FF 0%, #FFFFFF 100%);
-webkit-background-clip: text;
-webkit-text-fill-color: transparent;
        }
    }
}
</style>
