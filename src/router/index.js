import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                   // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true               // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect           // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    noCache: true                // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'               // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'             // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false            // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'   // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: (resolve) => require(['@/views/redirect'], resolve),
      },
    ],
  },
  {
    path: '/login',
    component: (resolve) => require(['@/views/login'], resolve),
    hidden: true,
  },
  {
    path: '/404',
    component: (resolve) => require(['@/views/error/404'], resolve),
    hidden: true,
  },
  {
    path: '/401',
    component: (resolve) => require(['@/views/error/401'], resolve),
    hidden: true,
  },
  {
    path: '/',
    component: (resolve) => require(['@/views/home/<USER>'], resolve),
    hidden: true,
  },
  {
    path: '/index',
    component: (resolve) => require(['@/views/home/<USER>'], resolve),
    hidden: true,
  },

  // IoT相关路由

  {
    path: '/iot',
    component: Layout,
    redirect: '/iot/index',
    name: 'IotManagement',
    meta: {
      title: 'IOT设备管理',
      icon: 'el-icon-s-check'
    },
    children: [
      {
        path: 'index',
        component: (resolve) => require(['@/views/iot/index'], resolve),
        name: 'IotIndex',
        meta: { title: 'iot首页', icon: 'el-icon-s-grid' }
      },
      {
        path: 'detail',
        component: (resolve) => require(['@/views/iot/detail'], resolve),
        name: 'IotDetail',
        meta: { title: 'iot详情', icon: 'el-icon-s-grid' },
        hidden: true
      },
      {
        path: 'car',
        component: (resolve) => require(['@/views/iot/carIndex'], resolve),
        name: 'CarIndex',
        meta: { title: 'CarIndex', icon: 'el-icon-s-grid' }
      },
      {
        path: 'car/detail',
        component: (resolve) => require(['@/views/iot/carDetail'], resolve),
        name: 'CarDetail',
        meta: { title: 'car详情', icon: 'el-icon-s-grid' },
      }
    ]
  },
  // 检测数据模板配置系统路由
  {
    path: '/detection',
    component: Layout,
    redirect: '/detection/data/list',
    name: 'Detection',
    meta: {
      title: '检测数据管理',
      icon: 'el-icon-s-check'
    },
    children: [
      {
        path: 'data/upload',
        component: (resolve) => require(['@/views/detection/data/upload'], resolve),
        name: 'DataUpload',
        meta: { title: '数据上传', icon: 'el-icon-upload2' }
      },
      {
        path: 'data/list',
        component: (resolve) => require(['@/views/detection/data/list'], resolve),
        name: 'DetectionDataList',
        meta: { title: '检测数据列表', icon: 'el-icon-s-order' }
      },
      {
        path: 'data/detail/:id',
        component: (resolve) => require(['@/views/detection/data/detail'], resolve),
        name: 'DetectionDataDetail',
        meta: { title: '检测数据详情', icon: 'el-icon-view' },
        hidden: true
      },

    ]
  },
  // 设备数据管理模块
  {
    path: '/device',
    component: Layout,
    redirect: '/device/export',
    name: 'DeviceData',
    meta: {
      title: '设备数据管理',
      icon: 'el-icon-s-platform'
    },
    children: [
      {
        path: 'export',
        component: (resolve) => require(['@/views/device/export/DeviceExport'], resolve),
        name: 'DeviceExport',
        meta: { title: '设备数据导出', icon: 'el-icon-download' }
      },
      {
        path: 'import',
        component: (resolve) => require(['@/views/device/import/DeviceImport'], resolve),
        name: 'DeviceImport',
        meta: { title: '设备数据导入', icon: 'el-icon-upload2' }
      },
      {
        path: 'export-history',
        component: (resolve) => require(['@/views/device/history/ExportHistory'], resolve),
        name: 'ExportHistory',
        meta: { title: '导出历史', icon: 'el-icon-document' }
      },
      {
        path: 'import-history',
        component: (resolve) => require(['@/views/device/history/ImportHistory'], resolve),
        name: 'ImportHistory',
        meta: { title: '导入历史', icon: 'el-icon-tickets' }
      }
    ]
  },
  {
    path: '/template',
    component: Layout,
    redirect: '/template/list',
    name: 'Template',
    meta: {
      title: 'Excel模板管理',
      icon: 'el-icon-s-check'
    },
    children: [
      {
        path: 'list',
        component: (resolve) => require(['@/views/detection/template/excel-template-list'], resolve),
        name: 'ExcelTemplateList',
        meta: { title: 'Excel模板列表', icon: 'el-icon-s-grid' }
      },
      {
        path: 'designer',
        component: (resolve) => require(['@/views/detection/template/excel-designer'], resolve),
        name: 'ExcelDesigner',
        meta: { title: 'Excel模板设计', icon: 'el-icon-edit', activeMenu: '/detection/template/excel-template-list' },
        hidden: true
      },
      {
        path: 'device-template-binding',
        component: (resolve) => require(['@/views/detection/template/device-template-binding'], resolve),
        name: 'DeviceTemplateBinding',
        meta: {title: '设备模板绑定', icon: 'el-icon-link'}
      }
    ]
  },
  {
    path: '/message',
    component: Layout,
    redirect: '/message/index',
    name: 'Message',
    meta: {
      title: '消息门户',
      icon: 'el-icon-s-check'
    },
    children: [
      {
        path: 'index',
        component: (resolve) => require(['@/views/messageportal/index'], resolve),
        name: 'MessagePortal',
        meta: { title: '消息门户首页', icon: 'el-icon-s-grid' }
      }
    ]
  },
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes,
})
