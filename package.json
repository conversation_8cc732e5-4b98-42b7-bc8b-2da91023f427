{"name": "logictrue-ui-iot", "version": "1.0.0", "description": "IoT设备检测数据管理系统", "author": "logictrue", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "keywords": ["vue", "iot", "detection", "data-management", "element-ui"], "dependencies": {"axios": "^0.21.0", "core-js": "3.8.1", "element-ui": "2.15.6", "file-saver": "^2.0.4", "js-cookie": "2.2.1", "jsencrypt": "^3.5.4", "moment": "^2.30.1", "nprogress": "0.2.0", "pikaz-excel-js": "^0.2.16", "qs": "^6.9.6", "svg-sprite-loader": "^6.0.11", "vue": "2.6.12", "vue-router": "3.4.9", "vue-seamless-scroll": "^1.1.23", "vuex": "3.6.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "^10.1.0", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "sass": "1.32.13", "sass-loader": "10.1.1", "vue-template-compiler": "2.6.12"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}